package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMajor;
import org.dromara.system.domain.SysMajorToMajorVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysMajorToMajorVoMapper.class},
    imports = {}
)
public interface MajorVoToSysMajorMapper extends BaseMapper<MajorVo, SysMajor> {
}
