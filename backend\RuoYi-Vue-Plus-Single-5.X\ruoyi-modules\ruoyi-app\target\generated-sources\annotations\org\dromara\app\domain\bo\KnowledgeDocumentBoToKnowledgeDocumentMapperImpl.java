package org.dromara.app.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeDocument;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:54:31+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class KnowledgeDocumentBoToKnowledgeDocumentMapperImpl implements KnowledgeDocumentBoToKnowledgeDocumentMapper {

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeDocument knowledgeDocument = new KnowledgeDocument();

        knowledgeDocument.setContent( arg0.getContent() );
        knowledgeDocument.setDocType( arg0.getDocType() );
        knowledgeDocument.setFilePath( arg0.getFilePath() );
        knowledgeDocument.setFileSize( arg0.getFileSize() );
        knowledgeDocument.setId( arg0.getId() );
        knowledgeDocument.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        knowledgeDocument.setMetadata( arg0.getMetadata() );
        knowledgeDocument.setOriginalFilename( arg0.getOriginalFilename() );
        knowledgeDocument.setProcessConfig( arg0.getProcessConfig() );
        knowledgeDocument.setProcessStatus( arg0.getProcessStatus() );
        knowledgeDocument.setRemark( arg0.getRemark() );
        knowledgeDocument.setSortOrder( arg0.getSortOrder() );
        knowledgeDocument.setSource( arg0.getSource() );
        knowledgeDocument.setStatus( arg0.getStatus() );
        knowledgeDocument.setSummary( arg0.getSummary() );
        knowledgeDocument.setTags( arg0.getTags() );
        knowledgeDocument.setTitle( arg0.getTitle() );

        return knowledgeDocument;
    }

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentBo arg0, KnowledgeDocument arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setContent( arg0.getContent() );
        arg1.setDocType( arg0.getDocType() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setId( arg0.getId() );
        arg1.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        arg1.setMetadata( arg0.getMetadata() );
        arg1.setOriginalFilename( arg0.getOriginalFilename() );
        arg1.setProcessConfig( arg0.getProcessConfig() );
        arg1.setProcessStatus( arg0.getProcessStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setSource( arg0.getSource() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setTags( arg0.getTags() );
        arg1.setTitle( arg0.getTitle() );

        return arg1;
    }
}
