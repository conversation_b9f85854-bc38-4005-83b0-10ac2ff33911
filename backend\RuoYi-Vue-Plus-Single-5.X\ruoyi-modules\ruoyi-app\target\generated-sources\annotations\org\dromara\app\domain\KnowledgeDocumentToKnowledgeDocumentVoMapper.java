package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__84;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__84.class,
    uses = {KnowledgeDocumentBoToKnowledgeDocumentMapper.class,KnowledgeDocumentVoToKnowledgeDocumentMapper.class},
    imports = {}
)
public interface KnowledgeDocumentToKnowledgeDocumentVoMapper extends BaseMapper<KnowledgeDocument, KnowledgeDocumentVo> {
}
