package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.MajorBoToSysMajorMapper;
import org.dromara.system.domain.vo.MajorVo;
import org.dromara.system.domain.vo.MajorVoToSysMajorMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {MajorVoToSysMajorMapper.class,MajorBoToSysMajorMapper.class},
    imports = {}
)
public interface SysMajorToMajorVoMapper extends BaseMapper<SysMajor, MajorVo> {
}
