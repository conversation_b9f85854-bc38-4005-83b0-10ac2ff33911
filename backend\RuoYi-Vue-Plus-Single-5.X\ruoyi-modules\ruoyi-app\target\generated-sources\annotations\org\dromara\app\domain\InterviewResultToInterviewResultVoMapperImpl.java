package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.InterviewResultVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:54:32+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class InterviewResultToInterviewResultVoMapperImpl implements InterviewResultToInterviewResultVoMapper {

    @Override
    public InterviewResultVo convert(InterviewResult arg0) {
        if ( arg0 == null ) {
            return null;
        }

        InterviewResultVo interviewResultVo = new InterviewResultVo();

        interviewResultVo.setAnsweredQuestions( arg0.getAnsweredQuestions() );
        interviewResultVo.setCompany( arg0.getCompany() );
        if ( arg0.getCreateTime() != null ) {
            interviewResultVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        interviewResultVo.setDate( arg0.getDate() );
        interviewResultVo.setDuration( arg0.getDuration() );
        interviewResultVo.setId( arg0.getId() );
        interviewResultVo.setJobId( arg0.getJobId() );
        interviewResultVo.setJobName( arg0.getJobName() );
        interviewResultVo.setMode( arg0.getMode() );
        interviewResultVo.setOverallFeedback( arg0.getOverallFeedback() );
        interviewResultVo.setPercentile( arg0.getPercentile() );
        interviewResultVo.setRank( arg0.getRank() );
        interviewResultVo.setRankText( arg0.getRankText() );
        interviewResultVo.setSessionId( arg0.getSessionId() );
        interviewResultVo.setStatus( arg0.getStatus() );
        List<String> list = arg0.getTopStrengths();
        if ( list != null ) {
            interviewResultVo.setTopStrengths( new ArrayList<String>( list ) );
        }
        List<String> list1 = arg0.getTopWeaknesses();
        if ( list1 != null ) {
            interviewResultVo.setTopWeaknesses( new ArrayList<String>( list1 ) );
        }
        interviewResultVo.setTotalQuestions( arg0.getTotalQuestions() );
        interviewResultVo.setTotalScore( arg0.getTotalScore() );
        if ( arg0.getUpdateTime() != null ) {
            interviewResultVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        interviewResultVo.setUserId( arg0.getUserId() );

        return interviewResultVo;
    }

    @Override
    public InterviewResultVo convert(InterviewResult arg0, InterviewResultVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAnsweredQuestions( arg0.getAnsweredQuestions() );
        arg1.setCompany( arg0.getCompany() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setDate( arg0.getDate() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setId( arg0.getId() );
        arg1.setJobId( arg0.getJobId() );
        arg1.setJobName( arg0.getJobName() );
        arg1.setMode( arg0.getMode() );
        arg1.setOverallFeedback( arg0.getOverallFeedback() );
        arg1.setPercentile( arg0.getPercentile() );
        arg1.setRank( arg0.getRank() );
        arg1.setRankText( arg0.getRankText() );
        arg1.setSessionId( arg0.getSessionId() );
        arg1.setStatus( arg0.getStatus() );
        if ( arg1.getTopStrengths() != null ) {
            List<String> list = arg0.getTopStrengths();
            if ( list != null ) {
                arg1.getTopStrengths().clear();
                arg1.getTopStrengths().addAll( list );
            }
            else {
                arg1.setTopStrengths( null );
            }
        }
        else {
            List<String> list = arg0.getTopStrengths();
            if ( list != null ) {
                arg1.setTopStrengths( new ArrayList<String>( list ) );
            }
        }
        if ( arg1.getTopWeaknesses() != null ) {
            List<String> list1 = arg0.getTopWeaknesses();
            if ( list1 != null ) {
                arg1.getTopWeaknesses().clear();
                arg1.getTopWeaknesses().addAll( list1 );
            }
            else {
                arg1.setTopWeaknesses( null );
            }
        }
        else {
            List<String> list1 = arg0.getTopWeaknesses();
            if ( list1 != null ) {
                arg1.setTopWeaknesses( new ArrayList<String>( list1 ) );
            }
        }
        arg1.setTotalQuestions( arg0.getTotalQuestions() );
        arg1.setTotalScore( arg0.getTotalScore() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
