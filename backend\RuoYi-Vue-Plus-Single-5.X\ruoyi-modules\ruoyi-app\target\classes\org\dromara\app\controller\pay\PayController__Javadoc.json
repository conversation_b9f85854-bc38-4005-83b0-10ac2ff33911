{"doc": " 支付控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPaymentOrder", "paramTypes": ["org.dromara.app.domain.dto.PaymentOrderDto", "jakarta.servlet.http.HttpServletRequest"], "doc": " 创建支付订单\n"}, {"name": "alipayPay", "paramTypes": ["java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": " 支付宝支付\n"}, {"name": "alipayNotify", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 支付宝异步通知\n"}, {"name": "alipayReturn", "paramTypes": ["jakarta.servlet.http.HttpServletRequest", "jakarta.servlet.http.HttpServletResponse"], "doc": " 支付宝同步回调\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": " 查询订单状态\n"}, {"name": "cancelOrder", "paramTypes": ["java.lang.String"], "doc": " 取消订单\n"}, {"name": "getClientIP", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": " 获取客户端IP地址\n"}, {"name": "buildSuccessHtml", "paramTypes": [], "doc": " 构建成功页面HTML\n"}, {"name": "buildErrorHtml", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建错误页面HTML\n"}], "constructors": []}