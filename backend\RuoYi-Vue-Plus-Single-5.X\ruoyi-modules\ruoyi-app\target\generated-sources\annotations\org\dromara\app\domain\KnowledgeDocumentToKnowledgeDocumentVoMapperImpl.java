package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:45:40+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class KnowledgeDocumentToKnowledgeDocumentVoMapperImpl implements KnowledgeDocumentToKnowledgeDocumentVoMapper {

    @Override
    public KnowledgeDocumentVo convert(KnowledgeDocument arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeDocumentVo knowledgeDocumentVo = new KnowledgeDocumentVo();

        knowledgeDocumentVo.setId( arg0.getId() );
        knowledgeDocumentVo.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        knowledgeDocumentVo.setTitle( arg0.getTitle() );
        knowledgeDocumentVo.setContent( arg0.getContent() );
        knowledgeDocumentVo.setDocType( arg0.getDocType() );
        knowledgeDocumentVo.setSource( arg0.getSource() );
        knowledgeDocumentVo.setOriginalFilename( arg0.getOriginalFilename() );
        knowledgeDocumentVo.setFilePath( arg0.getFilePath() );
        knowledgeDocumentVo.setFileSize( arg0.getFileSize() );
        knowledgeDocumentVo.setStatus( arg0.getStatus() );
        knowledgeDocumentVo.setProcessStatus( arg0.getProcessStatus() );
        knowledgeDocumentVo.setVectorCount( arg0.getVectorCount() );
        knowledgeDocumentVo.setSummary( arg0.getSummary() );
        knowledgeDocumentVo.setTags( arg0.getTags() );
        knowledgeDocumentVo.setMetadata( arg0.getMetadata() );
        knowledgeDocumentVo.setProcessConfig( arg0.getProcessConfig() );
        knowledgeDocumentVo.setErrorMessage( arg0.getErrorMessage() );
        knowledgeDocumentVo.setLastProcessTime( arg0.getLastProcessTime() );
        knowledgeDocumentVo.setSortOrder( arg0.getSortOrder() );
        knowledgeDocumentVo.setRemark( arg0.getRemark() );
        knowledgeDocumentVo.setCreateDept( arg0.getCreateDept() );
        knowledgeDocumentVo.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeDocumentVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        knowledgeDocumentVo.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeDocumentVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }

        return knowledgeDocumentVo;
    }

    @Override
    public KnowledgeDocumentVo convert(KnowledgeDocument arg0, KnowledgeDocumentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setContent( arg0.getContent() );
        arg1.setDocType( arg0.getDocType() );
        arg1.setSource( arg0.getSource() );
        arg1.setOriginalFilename( arg0.getOriginalFilename() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setProcessStatus( arg0.getProcessStatus() );
        arg1.setVectorCount( arg0.getVectorCount() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setTags( arg0.getTags() );
        arg1.setMetadata( arg0.getMetadata() );
        arg1.setProcessConfig( arg0.getProcessConfig() );
        arg1.setErrorMessage( arg0.getErrorMessage() );
        arg1.setLastProcessTime( arg0.getLastProcessTime() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }

        return arg1;
    }
}
