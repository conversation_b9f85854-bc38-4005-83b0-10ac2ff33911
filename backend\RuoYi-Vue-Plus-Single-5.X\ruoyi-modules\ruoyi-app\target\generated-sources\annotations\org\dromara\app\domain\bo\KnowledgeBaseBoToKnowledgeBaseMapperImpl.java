package org.dromara.app.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeBase;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:45:40+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class KnowledgeBaseBoToKnowledgeBaseMapperImpl implements KnowledgeBaseBoToKnowledgeBaseMapper {

    @Override
    public KnowledgeBase convert(KnowledgeBaseBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeBase knowledgeBase = new KnowledgeBase();

        knowledgeBase.setId( arg0.getId() );
        knowledgeBase.setName( arg0.getName() );
        knowledgeBase.setDescription( arg0.getDescription() );
        knowledgeBase.setType( arg0.getType() );
        knowledgeBase.setStatus( arg0.getStatus() );
        knowledgeBase.setVectorDimension( arg0.getVectorDimension() );
        knowledgeBase.setIndexConfig( arg0.getIndexConfig() );
        knowledgeBase.setExtendConfig( arg0.getExtendConfig() );
        knowledgeBase.setSortOrder( arg0.getSortOrder() );
        knowledgeBase.setRemark( arg0.getRemark() );

        return knowledgeBase;
    }

    @Override
    public KnowledgeBase convert(KnowledgeBaseBo arg0, KnowledgeBase arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setType( arg0.getType() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setVectorDimension( arg0.getVectorDimension() );
        arg1.setIndexConfig( arg0.getIndexConfig() );
        arg1.setExtendConfig( arg0.getExtendConfig() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
