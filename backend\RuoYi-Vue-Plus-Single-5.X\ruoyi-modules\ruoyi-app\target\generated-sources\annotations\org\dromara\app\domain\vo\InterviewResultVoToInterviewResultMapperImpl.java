package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.InterviewResult;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T12:54:31+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class InterviewResultVoToInterviewResultMapperImpl implements InterviewResultVoToInterviewResultMapper {

    @Override
    public InterviewResult convert(InterviewResultVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        InterviewResult interviewResult = new InterviewResult();

        if ( arg0.getCreateTime() != null ) {
            interviewResult.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            interviewResult.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        interviewResult.setAnsweredQuestions( arg0.getAnsweredQuestions() );
        interviewResult.setCompany( arg0.getCompany() );
        interviewResult.setDate( arg0.getDate() );
        interviewResult.setDuration( arg0.getDuration() );
        interviewResult.setId( arg0.getId() );
        interviewResult.setJobId( arg0.getJobId() );
        interviewResult.setJobName( arg0.getJobName() );
        interviewResult.setMode( arg0.getMode() );
        interviewResult.setOverallFeedback( arg0.getOverallFeedback() );
        interviewResult.setPercentile( arg0.getPercentile() );
        interviewResult.setRank( arg0.getRank() );
        interviewResult.setRankText( arg0.getRankText() );
        interviewResult.setSessionId( arg0.getSessionId() );
        interviewResult.setStatus( arg0.getStatus() );
        List<String> list = arg0.getTopStrengths();
        if ( list != null ) {
            interviewResult.setTopStrengths( new ArrayList<String>( list ) );
        }
        List<String> list1 = arg0.getTopWeaknesses();
        if ( list1 != null ) {
            interviewResult.setTopWeaknesses( new ArrayList<String>( list1 ) );
        }
        interviewResult.setTotalQuestions( arg0.getTotalQuestions() );
        interviewResult.setTotalScore( arg0.getTotalScore() );
        interviewResult.setUserId( arg0.getUserId() );

        return interviewResult;
    }

    @Override
    public InterviewResult convert(InterviewResultVo arg0, InterviewResult arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setAnsweredQuestions( arg0.getAnsweredQuestions() );
        arg1.setCompany( arg0.getCompany() );
        arg1.setDate( arg0.getDate() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setId( arg0.getId() );
        arg1.setJobId( arg0.getJobId() );
        arg1.setJobName( arg0.getJobName() );
        arg1.setMode( arg0.getMode() );
        arg1.setOverallFeedback( arg0.getOverallFeedback() );
        arg1.setPercentile( arg0.getPercentile() );
        arg1.setRank( arg0.getRank() );
        arg1.setRankText( arg0.getRankText() );
        arg1.setSessionId( arg0.getSessionId() );
        arg1.setStatus( arg0.getStatus() );
        if ( arg1.getTopStrengths() != null ) {
            List<String> list = arg0.getTopStrengths();
            if ( list != null ) {
                arg1.getTopStrengths().clear();
                arg1.getTopStrengths().addAll( list );
            }
            else {
                arg1.setTopStrengths( null );
            }
        }
        else {
            List<String> list = arg0.getTopStrengths();
            if ( list != null ) {
                arg1.setTopStrengths( new ArrayList<String>( list ) );
            }
        }
        if ( arg1.getTopWeaknesses() != null ) {
            List<String> list1 = arg0.getTopWeaknesses();
            if ( list1 != null ) {
                arg1.getTopWeaknesses().clear();
                arg1.getTopWeaknesses().addAll( list1 );
            }
            else {
                arg1.setTopWeaknesses( null );
            }
        }
        else {
            List<String> list1 = arg0.getTopWeaknesses();
            if ( list1 != null ) {
                arg1.setTopWeaknesses( new ArrayList<String>( list1 ) );
            }
        }
        arg1.setTotalQuestions( arg0.getTotalQuestions() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
