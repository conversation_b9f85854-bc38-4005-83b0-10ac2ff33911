{"doc": " 认证控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendSmsCode", "paramTypes": ["java.util.Map"], "doc": " 发送手机验证码\n"}, {"name": "sendEmailCode", "paramTypes": ["java.util.Map"], "doc": " 发送邮箱验证码\n"}, {"name": "loginByPhone", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 手机号验证码登录\n"}, {"name": "loginByPassword", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 密码登录\n"}, {"name": "register", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 用户注册\n"}, {"name": "checkPhoneExists", "paramTypes": ["java.util.Map"], "doc": " 检查手机号是否已注册\n"}, {"name": "checkEmailExists", "paramTypes": ["java.util.Map"], "doc": " 检查邮箱是否已注册\n"}, {"name": "resetPassword", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 重置密码\n"}, {"name": "thirdParty<PERSON><PERSON>in", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 第三方登录 (基础实现，可根据需要扩展)\n"}, {"name": "refreshToken", "paramTypes": ["org.dromara.app.domain.dto.AppAuthDto"], "doc": " 刷新Token\n"}, {"name": "logout", "paramTypes": [], "doc": " 退出登录\n"}, {"name": "getCurrentUser", "paramTypes": [], "doc": " 获取当前用户信息\n"}], "constructors": []}