{"doc": " 讯飞星火大模型聊天控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "chat", "paramTypes": ["org.dromara.app.controller.chat.SparkController.ChatRequest"], "doc": " 聊天接口\n"}, {"name": "chatStream", "paramTypes": ["org.dromara.app.controller.chat.SparkController.ChatRequest"], "doc": " 流式聊天接口\n"}, {"name": "agent<PERSON><PERSON>", "paramTypes": ["org.dromara.app.controller.chat.SparkController.AgentChatRequest"], "doc": " 智能体聊天接口\n"}, {"name": "agentChatStream", "paramTypes": ["org.dromara.app.controller.chat.SparkController.AgentChatRequest"], "doc": " 智能体流式聊天接口\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.app.service.impl.XunfeiServiceImpl.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "org.dromara.app.service.impl.XunfeiServiceImpl.StreamingChatResponseHandler"], "doc": " 调用流式Agent服务\n"}, {"name": "buildContext", "paramTypes": ["org.dromara.app.controller.chat.SparkController.ChatRequest"], "doc": " 构建上下文\n"}], "constructors": []}