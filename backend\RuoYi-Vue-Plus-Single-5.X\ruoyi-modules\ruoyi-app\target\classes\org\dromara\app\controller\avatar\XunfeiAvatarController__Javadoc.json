{"doc": " 讯飞数字人控制器\n\n <AUTHOR>\n @date 2025-07-20\n", "fields": [], "enumConstants": [], "methods": [{"name": "startSession", "paramTypes": ["org.dromara.app.domain.dto.avatar.AvatarStartDto"], "doc": " 启动数字人会话\n"}, {"name": "sendTextDriver", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": " 发送文本驱动\n"}, {"name": "sendTextInteract", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.avatar.AvatarTextDto"], "doc": " 发送文本交互\n"}, {"name": "sendAudioDriver", "paramTypes": ["java.lang.String", "java.lang.String", "int"], "doc": " 发送音频驱动\n"}, {"name": "resetAvatar", "paramTypes": ["java.lang.String"], "doc": " 重置数字人\n"}, {"name": "stopSession", "paramTypes": ["java.lang.String"], "doc": " 停止数字人会话\n"}, {"name": "sendHeartbeat", "paramTypes": ["java.lang.String"], "doc": " 发送心跳\n"}, {"name": "sendAction", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送动作指令\n"}, {"name": "getSessionInfo", "paramTypes": ["java.lang.String"], "doc": " 获取会话信息\n"}, {"name": "checkSessionStatus", "paramTypes": ["java.lang.String"], "doc": " 检查会话状态\n"}, {"name": "getStreamUrl", "paramTypes": ["java.lang.String"], "doc": " 获取推流地址\n"}, {"name": "getStreamStatus", "paramTypes": ["java.lang.String"], "doc": " 检查推流地址状态\n"}, {"name": "updateStreamUrl", "paramTypes": ["java.lang.String"], "doc": " 更新推流地址\n"}], "constructors": []}